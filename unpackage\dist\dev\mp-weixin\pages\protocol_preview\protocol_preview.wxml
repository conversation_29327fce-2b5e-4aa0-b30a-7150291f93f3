<view class="protocol-preview-container data-v-3f6102ab"><view wx:if="{{a}}" class="long-image-preview data-v-3f6102ab"><view class="preview-header data-v-3f6102ab"><view class="header-right data-v-3f6102ab"><text class="progress-text data-v-3f6102ab">{{b}}%</text></view></view><scroll-view class="long-scroll-container data-v-3f6102ab" scroll-y="true" scroll-top="{{f}}" bindscroll="{{g}}" enhanced show-scrollbar="{{false}}"><view class="content-container data-v-3f6102ab"><view wx:if="{{c}}" class="loading-container data-v-3f6102ab"><view class="loading-icon data-v-3f6102ab"><view class="fas fa-spinner fa-spin data-v-3f6102ab"></view></view><text class="loading-text data-v-3f6102ab">正在加载协议内容...</text></view><view wx:else class="images-container data-v-3f6102ab"><view wx:for="{{d}}" wx:for-item="image" wx:key="e" class="svg-container data-v-3f6102ab"><image src="{{image.a}}" mode="widthFix" class="{{['protocol-page', 'svg-image', 'data-v-3f6102ab', image.b && 'first-page']}}" bindload="{{image.c}}" binderror="{{image.d}}" lazy-load/></view><image wx:for="{{e}}" wx:for-item="image" wx:key="a" src="{{image.b}}" mode="widthFix" class="{{['protocol-page', 'data-v-3f6102ab', image.c && 'first-page']}}" bindload="{{image.d}}" binderror="{{image.e}}" lazy-load/></view></view></scroll-view><view class="progress-indicator data-v-3f6102ab"><view class="progress-bar data-v-3f6102ab"><view class="progress-fill data-v-3f6102ab" style="{{'width:' + h}}"></view></view></view></view></view>