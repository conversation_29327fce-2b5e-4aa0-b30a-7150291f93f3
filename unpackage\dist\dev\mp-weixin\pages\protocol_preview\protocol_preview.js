"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "protocol_preview",
  setup(__props) {
    const receivedFileUrl = common_vendor.ref("");
    const receivedFileType = common_vendor.ref("");
    const receivedFileName = common_vendor.ref("");
    const receivedCaseNumber = common_vendor.ref("");
    const fileUrl = common_vendor.ref("http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx");
    const pdfImagesBaseUrl = common_vendor.ref("http://192.168.1.101:10010/pdf_images/scheme/485/");
    const fileType = common_vendor.ref("pdf");
    const fileName = common_vendor.ref("调解协议.pdf");
    const showImagePreview = common_vendor.ref(false);
    const pdfImages = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const loadedImages = common_vendor.ref(/* @__PURE__ */ new Set());
    const scrollTop = common_vendor.ref(0);
    const scrollProgress = common_vendor.ref(0);
    common_vendor.ref(0);
    common_vendor.ref(0);
    const svgImages = common_vendor.computed(() => {
      return pdfImages.value.map((image, index) => ({ ...image, originalIndex: index })).filter((image) => image.type === "svg");
    });
    const normalImages = common_vendor.computed(() => {
      return pdfImages.value.map((image, index) => ({ ...image, originalIndex: index })).filter((image) => !image.type || image.type !== "svg");
    });
    const viewAsImages = async () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        const images = await getPdfImages();
        if (images.length > 0) {
          pdfImages.value = images;
        } else {
          common_vendor.index.showToast({
            title: "暂无可预览内容",
            icon: "none"
          });
          closeImagePreview();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:158", "加载图片预览失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请稍后重试",
          icon: "error"
        });
        closeImagePreview();
      } finally {
        isLoading.value = false;
      }
    };
    const getPdfImages = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      return [
        { url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },
        { url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },
        { url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },
        { url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },
        { url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }
      ];
    };
    const onScroll = (e) => {
      const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;
      scrollTop.value = currentScrollTop;
      if (scrollHeight > clientHeight) {
        scrollProgress.value = currentScrollTop / (scrollHeight - clientHeight) * 100;
      }
    };
    const onImageLoad = (index) => {
      loadedImages.value.add(index);
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:206", `图片 ${index + 1} 加载成功`);
    };
    const onImageError = (index) => {
      common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:213", `图片 ${index + 1} 加载失败`);
      common_vendor.index.showToast({
        title: `第${index + 1}页加载失败`,
        icon: "none",
        duration: 2e3
      });
    };
    const closeImagePreview = () => {
      showImagePreview.value = false;
      scrollTop.value = 0;
      scrollProgress.value = 0;
      loadedImages.value.clear();
    };
    const showSvgPreview = () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        pdfImages.value = [{
          url: fileUrl.value,
          page: 1,
          type: "svg"
        }];
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:246", "SVG文件预览准备完成:", fileUrl.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:248", "SVG预览失败:", error);
        common_vendor.index.showToast({
          title: "SVG预览失败",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:355", "预览页面参数:", options);
      if (options.fileUrl) {
        try {
          receivedFileUrl.value = decodeURIComponent(options.fileUrl);
          fileUrl.value = receivedFileUrl.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:362", "接收到文件URL:", fileUrl.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:364", "fileUrl参数解码失败:", error);
          receivedFileUrl.value = options.fileUrl;
          fileUrl.value = options.fileUrl;
        }
      }
      if (options.fileType) {
        try {
          receivedFileType.value = decodeURIComponent(options.fileType);
          fileType.value = receivedFileType.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:375", "接收到文件类型:", fileType.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:377", "fileType参数解码失败:", error);
          receivedFileType.value = options.fileType;
          fileType.value = options.fileType;
        }
      }
      if (options.fileName) {
        try {
          receivedFileName.value = decodeURIComponent(options.fileName);
          fileName.value = receivedFileName.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:388", "接收到文件名:", fileName.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:390", "fileName参数解码失败:", error);
          receivedFileName.value = options.fileName;
          fileName.value = options.fileName;
        }
      }
      if (options.caseNumber) {
        try {
          receivedCaseNumber.value = decodeURIComponent(options.caseNumber);
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:400", "接收到案件号:", receivedCaseNumber.value);
          pdfImagesBaseUrl.value = `http://192.168.1.101:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:404", "caseNumber参数解码失败:", error);
          receivedCaseNumber.value = options.caseNumber;
          pdfImagesBaseUrl.value = `http://192.168.1.101:10010/pdf_images/scheme/${options.caseNumber}/`;
        }
      }
    });
    common_vendor.onMounted(() => {
      const title = fileName.value ? `预览 - ${fileName.value}` : "文件预览";
      common_vendor.index.setNavigationBarTitle({ title });
      if (fileType.value === "svg") {
        showSvgPreview();
      } else if (fileType.value === "pdf" || fileType.value === "docx") {
        viewAsImages();
      } else {
        viewAsImages();
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showImagePreview.value
      }, showImagePreview.value ? common_vendor.e({
        b: common_vendor.t(Math.round(scrollProgress.value)),
        c: isLoading.value
      }, isLoading.value ? {} : {
        d: common_vendor.f(svgImages.value, (image, index, i0) => {
          return {
            a: image.url,
            b: index === 0 ? 1 : "",
            c: common_vendor.o(($event) => onImageLoad(image.originalIndex), `svg-${index}`),
            d: common_vendor.o(($event) => onImageError(image.originalIndex), `svg-${index}`),
            e: `svg-${index}`
          };
        }),
        e: common_vendor.f(normalImages.value, (image, index, i0) => {
          return {
            a: `img-${index}`,
            b: image.url,
            c: index === 0 ? 1 : "",
            d: common_vendor.o(($event) => onImageLoad(image.originalIndex), `img-${index}`),
            e: common_vendor.o(($event) => onImageError(image.originalIndex), `img-${index}`)
          };
        })
      }, {
        f: scrollTop.value,
        g: common_vendor.o(onScroll),
        h: scrollProgress.value + "%"
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f6102ab"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/protocol_preview/protocol_preview.js.map
